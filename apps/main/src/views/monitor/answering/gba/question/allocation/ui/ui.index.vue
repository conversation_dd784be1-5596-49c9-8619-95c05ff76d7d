<template>
    <div class="ui-index-container">
        <Text text="三、共享任务" font-size="24px" :font-weight="600" :stroke-width="2" />
        <TotalFund />
        <PlayerCard
            :fundAnimationConfig="{ show, text: '-¥5', action: '-' }"
            playerName="我"
            coin="666"
            size="small"
            :castShadow="true"
            :showJoin="show"
            @fundStartVanish="onPplFundStartVanish"
            @fundAnimationEnd="onPplFundAnimationEnd"
        />
        <InvestmentResult
            v-if="visible"
            :type="type"
            :successMinParticipant="4"
            :currentRoundSnapshot="3"
            :realityParticipant="3"
            :reward="200"
            :distributeTime="20"
            @assignStartAppear="type = TypeEnum.分配"
        />
        <b-button @click="show = !show"> 1111 </b-button>

        <!-- 参与 启用 -->
        <Button style="width: 114px" :isActiveEffect="true" :disabled="false">
            <Text
                text="参与"
                font-size="20px"
                :font-weight="600"
                :stroke-width="1"
                textColor="rgba(248, 255, 220, 1)"
                strokeColor="rgba(81, 164, 15, 1)"
                :shadowFilterConfig="{ x: 0, y: 0.92, blur: 0.92, color: '#419C02' }"
            />
        </Button>
        <!-- 参与 禁用 -->
        <Button style="width: 114px" :isActiveEffect="false" :disabled="true">
            <Text
                text="参与"
                font-size="20px"
                :font-weight="600"
                :stroke-width="1"
                textColor="rgba(252, 255, 238, 1)"
                strokeColor="rgba(168, 210, 135, 1)"
                :shadowFilterConfig="{ x: 0, y: 0.92, blur: 0.92, color: '#A3CF83' }"
            />
        </Button>
        <!-- 不参与 启用 -->
        <Button style="width: 114px" :isActiveEffect="true" :disabled="false" theme="orange">
            <Text
                text="不参与"
                font-size="20px"
                :font-weight="600"
                :stroke-width="1"
                textColor="rgba(255, 249, 220, 1)"
                strokeColor="rgba(252, 115, 19, 1)"
                :shadowFilterConfig="{ x: 0, y: 0.92, blur: 0.92, color: '#E55711' }"
            />
        </Button>
        <!-- 不参与 禁用 -->
        <Button style="width: 114px" :isActiveEffect="false" :disabled="true" theme="orange">
            <Text
                text="不参与"
                font-size="20px"
                :font-weight="600"
                :stroke-width="1"
                textColor="rgba(255, 252, 238, 1)"
                strokeColor="rgba(254, 185, 137, 1)"
                :shadowFilterConfig="{ x: 0, y: 0.92, blur: 0.92, color: '#FDAC74' }"
            />
        </Button>
        <!-- 参与√ 禁用 -->
        <Button style="width: 114px" :isActiveEffect="false" :disabled="true">
            <img style="margin-top: 2.5px" src="https://img.bosszhipin.com/static/file/2024/qh3ljnjnc21726827009333.svg" alt="" />
        </Button>
        <!-- 不参与√ 禁用 -->
        <Button style="width: 114px" :isActiveEffect="false" :disabled="true" theme="orange">
            <img style="margin-top: 2.5px" src="https://img.bosszhipin.com/static/file/2024/bfoavj7jf31726827009161.svg" alt="" />
        </Button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Text from '../../../component/text.vue';
import TotalFund from './total-fund.vue';
import PlayerCard from './player-card.vue';
import Button from '../../../component/button.vue';
import InvestmentResult, { TypeEnum } from './investment-result.vue';

defineOptions({
    name: 'UiIndex',
});
defineProps({
    propName: {
        type: Object,
        default: () => ({}),
    },
});
const visible = ref(false);
const type = ref<TypeEnum>(TypeEnum.成功);
setTimeout(() => {
    visible.value = false;
}, 1500);

const show = ref(true);
function onPplFundStartVanish() {
    console.log('玩家资金开始消失');
}
function onPplFundAnimationEnd() {
    console.log('玩家资金动画播放结束');
}
</script>
<style lang="less" scoped>
.ui-index-container {
    width: 100vw;
    height: 100vh;
    background: linear-gradient(179.99998deg, #7edaff 4%, #dde6ff 47%, #3696fc 100%);
    box-shadow: inset 0px 4px 35px 0px rgba(0, 74, 123, 0.23);
}
</style>
